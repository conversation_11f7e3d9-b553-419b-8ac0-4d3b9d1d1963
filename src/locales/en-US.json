{"meta": {"title": "IQUNIX EZ"}, "language": "English", "play_your_way_with_ez": "PLAY YOUR WAY WITH EZ!", "effortlessly_conlgure_your_ez": "Effortlessly configure your EZ here, explore unique settings, and personalize your device to your liking. ", "effortlessly_conlgure_your_ez2": "Easy access, easy configuration, easy saving - Easy to win!", "get_start": "Get Started", "enter_driver": "Get Started", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "next_step": "Next", "finish": "Finish", "home": {"initializing": "Initializing...", "find_device": "<PERSON>ce Detected", "device_initialized": "Device Initialized", "device_loaded": "Loaded Successfully", "find_device_tip": "<PERSON><PERSON>"}, "sidebar": {"keymap": "Keymap", "light": "Lighting", "performance": "Performance", "advanced_key": "Pro Keys", "career_preset": "Professional Presets", "keytest": "Key Calibration", "settings": "Settings"}, "keytest_tab": {"logs": "Key Test", "professional": "Pro FPS Calibration", "fast": "Quick Calibration", "content_alert_completed": "Calibration Complete", "start_calibration": "Start Calibration", "save_calibration": "Save Calibration", "steps_content_title": "Recalibrate after unplugging, changing switches, or updating firmware to prevent key issues like chatter or disconnection.", "steps_content_fast_main": "Quick Calibration Steps", "steps_content_main": "Pro FPS Calibration Steps:", "steps_content_note1": "1. Click the Start Calibration", "steps_content_note2": "2. <PERSON>y press the W/A/S/D, Tab, Left Shift, and Left Ctrl keys 5 times until the numbers in the circles disappear.", "steps_content_fast_note2": "2. <PERSON><PERSON> press all keys on the keyboard 2 times until the numbers in the circles disappear", "steps_content_note3": "3. Click the Save Calibration button to complete the process"}, "keyboard": {"select_all": "Select All", "invert_selection": "Deselect All", "clear_selection": "Cancel", "main_layer": "Main Layer", "secondly_layer": "Fn Layer 1"}, "keymap": {"basic": "Basic", "light": "Lighting", "multimedia": "Multimedia", "mouse": "Mouse", "other": "Advanced", "layer": "Layer", "clicktitle": "Keymap", "press_sec_switch": "Hold 5s to Switch Profiles", "click_cut_layer": "Tap to Switch Layers", "Fclick_title": "F Key", "system_title": "Windows Functions", "system_title_osx": "macOS Functions", "click_quick_switch": "Tap to Toggle Pro Keys", "syskey": {"system_com_close": "Power Off", "system_commac_close": "Power Off", "system_com_sleep": "Sleep", "system_com_wake": "Wake", "system_com_search": "Search", "system_com_calc": "Open Calculator", "system_com_mycom": "Open File Explorer", "system_win_lock": "WIN Lock", "system_com_mail": "Open Mail"}, "otherkey": {"click_quick_socd": "SOCD"}, "layerkey": {"layer_mode_a1": "Custom Profile", "layer_mode_a2": "Office Profile", "layer_mode_a3": "Esport Profile", "layer_mode_mo0": "Main Layer", "layer_mode_mo1": "Fn Layer 1"}, "lightkey": {"lamp_effect_switch": "RGB Toggle", "lamp_bright_up": "Brightness +", "lamp_bright_down": "Brightness -", "lamp_speed_up": "Speed +", "lamp_speed_down": "Speed - ", "lamp_effect_mode": "RGB Mode", "lamp_active_direction": "Switch Direction", "mult_color_cycling": "Color Cycling"}, "multimediakey": {"mult_music": "Media Player", "mult_next": "Next Track", "mult_nomu": "Mute", "mult_play": "Play/Pause", "mult_up": "Previous Track", "mult_voice_down": "Volume -", "mult_voice_up": "Volume +"}, "mousekey": {"mouse_left": "Left Click", "mouse_right": "Right Click", "mouse_sliod_click": "Middle Click", "mouse_d_left": "Double Left Click", "mouse_d_right": "Double Right Click", "mouse_up_siod": "Scroll Up", "mouse_down_sliod": "Scroll Down", "mouse_l_sliod": "<PERSON><PERSON> Left", "mouse_r_sliod": "Scroll Right", "mouse_forward": "<PERSON> 5", "mouse_back": "<PERSON> 4"}, "basic_key": {"blank_key": "Empty Keys", "letter_key": "Alphanumeric Keys", "number_key": "Numbers", "function_key": "Symbols and Function Keys", "f_key": "F Row", "f_key_extend": "Extended F Row", "modifier_key": "Modifier Keys", "function_key_extend": "Function Area", "shift_symbol": "Shift"}, "other_key": {"lock_key": "Lock", "advanced_key": "Advanced Keys", "accuracy_layer": "Precision Layer", "socd_switch": "SOCD Switch"}, "layer_key": {"switch_layer": "Switch Layer"}}, "light": {"light_preset": "Light Presets", "global_brightness": "Global Brightness", "global_speed": "Global Speed", "light_mode": {"0": "Disabled", "46": "Custom", "1": "Global Static", "2": "Main + Function Area", "3": "Vertical Gradient", "4": "Horizontal Gradient", "5": "Breathing", "6": "S-Band", "7": "V-Band", "8": "S-Wheel", "9": "V-Wheel", "10": "S-Spiral", "11": "V-Spiral", "12": "Full Color Cycle", "13": "Left-Right Cycle", "14": "Up-Down Cycle", "15": "Rainbow Cycle", "16": "In-Out Cycle", "17": "Dual In-Out Cycle", "18": "Windmill Cycle", "19": "Spiral Cycle", "20": "Dual Beacon", "21": "Rainbow Beacon", "22": "Rainbow Windmill", "23": "Raindrop", "24": "Rainbow Raindrop", "25": "<PERSON><PERSON> Breathing", "26": "<PERSON><PERSON>", "27": "Hue Wave", "28": "Pixel Rain", "29": "Pixel Flow", "30": "Pixel Fractal", "31": "Typing Heatmap", "32": "Digital Rain", "33": "Simple Solid Color", "34": "Solid Color", "35": "Wide Solid Color", "36": "Multicolor Wide", "37": "Cross Solid Color", "38": "Multicolor Cross", "39": "Node Color", "40": "Multicolor Nodes", "41": "Sputter", "42": "Multicolor Sputter", "43": "Single Color Sputter", "44": "Multicolor Single Color Sputter"}}, "performance": {"please_select_key": "Please select a key", "axis_selection": "Select Switch", "performance_setting": "Performance Setting", "loading_text_1": "The accuracy is being calibrated in real-time, please be patient.", "loading_text_2": "Getting ready for your victory, please wait a moment.", "xingguicizhou": "Star Trail", "ciyu_gaming": "Magnetic Jade Gaming", "ciyu": "Magnetic Jade", "ciyu_pro": "Magnetic Jade Pro", "ciyu_max": "Magnetic Jade Max", "shenmi_x": "X Magnetic Switch", "trigger_point": "Actuation Point", "quick_trigger_mode": "Rapid Trigger", "quick_trigger_mode_tip": "Set RT sensitivity for press activation and release.", "press_trigger_point": "Press (Activate)", "release_trigger_point": "Release (Deactivate)", "bottom_safe_area": "Bottom Safety Zone", "messages": {"use_warning": "Warning", "use_warning_bottom_safe_area": "Due to tolerance in the magnetic switches and slight hand vibrations, setting the deadzone to 0 may impact stability.\nThe 0.005mm rapid trigger supports a 0 safety zone, showcasing EZ's self-developed technology and ensuring data accuracy. We recommend using Tournament Mode for the best gaming experience.", "warm_tips": "Tips", "warm_tips_crazy_mode": "The 0.005mm distance is undetectable by the human touch. In Infinite Mode, the keys support rapid triggers at 0.005mm, mainly showcasing EZ's performance without enhancing the gaming experience."}, "tournament_mode": "Tournament Mode", "esports_mode": "eSports Mode", "beserk_mode": "Fury Mode", "limitless_mode": "Infinite Mode", "switch_tournament_mode_tip": "Switched to Tournament Mode", "switch_esports_mode_tip": "Switched to eSports Mode", "switch_beserk_mode_tip": "Switched to Fury Mode", "switch_limitless_mode_tip": "Switched to Infinite Mode", "popover_content": {"quick_trigger_mode_content": "Set the actuation point to your preferred feel — lower is better, but it may make accidental presses more likely.", "release_trigger_point_content": "For rapid trigger, lower values are better unless they cause accidental releases. If issues occur, please increase the 'Press (Activate)' value."}}, "advanced_key": {"advanced_key_management": "Binding Keys", "advanced_key_management_list": "Binding Keys", "advanced_key_settings": "Binding Settings", "new_advanced_key": "Create", "new_advanced_key_tip": "Create a new binding", "list": {"rs_tip": "<PERSON><PERSON><PERSON> monitors the 2 selected keys and activates whichever key is pressed down further.", "socd_tip": "SOCD monitors the 2 selected keys and activates them based on your chosen settings.", "mt_tip": "Two different actions on one key: hold for the first action, tap for the second.", "dks_tip": "One key, four functions: Bind 1 to 4 actions based on four different pressure levels.", "tgl_tip": "Tap the key to toggle continuous trigger on or off. Hold the key for normal trigger behavior."}, "rs": {"advanced_keyrs_title": "<PERSON><PERSON>", "advanced_keyrs_content": "Select two keys from your keyboard or the preview above to assign to <PERSON><PERSON>nap<PERSON>. This mode will monitor the two selected keys and trigger whichever one is pressed further. When both keys are fully pressed, both will be activated."}, "socd": {"choose_two_keys": "Select 2 keys to assign", "advanced_keyrs_title": "Snap Tap (SOCD)", "advanced_keyrs_content": "Select two keys from the keyboard or the preview above to assign to SOCD. This mode monitors the two selected keys and activates them based on your chosen settings.", "advanced_socdmodes_title": "Snap Tap (SOCD) Configuration", "advanced_socdmodes_content": "Choose the key response when both keys are pressed at the same time.", "advanced_socdmodes_alert": "For a more natural feel, both keys will activate when fully pressed together.", "choose_key_1": "Key 1", "choose_key_2": "Key 2", "choose_related_function": "Select what happens when both keys are activated.", "back_cover": "Last Input Priority", "mutual_exclusion": "Neutral", "priority": "Absolute Priority", "key_1": "Key 1", "key_2": "Key 2", "choose": "Select"}, "mt": {"mt_config": "<PERSON><PERSON>", "current_key": "Current Key", "choose_keyboard": "Select Key", "choose_key": "Select Key", "hold_key": "Hold", "single_click": "Tap", "hold_time_tip": "Manually adjust hold time (default: 200ms)", "advanced_keyrs_title": "<PERSON><PERSON> (MT)", "advanced_keyrs_content": "Select a key from your keyboard or the preview above to assign a Mod Tap (MT) function. A single key can perform two actions: tap for one function, hold for another. Drag and drop the key to your desired location to assign the Mod Tap.", "advanced_mtmodes_title": "Manually adjust hold time", "advanced_mtmodes_content": "Manually adjust the time required to trigger the \"hold\" function (default is 200ms). Tip: If the \"hold\" function is bound to a modifier key combined with another key, the hold will trigger immediately—unless the other key is also an MT key."}, "dks": {"dks_config": "Dynamic Keystroke (DKS)", "choose_keyboard": "Select Key", "choose_key": "Select Key", "choose_key_1": "Select Key 1", "hold_key": "Hold", "advanced_modes_content": "Select the keys to bind in the pop-up window. Click the plus icon for a single trigger, or drag it for continuous triggering.", "single_click": "Tap", "press_start": "Start Press", "press_bottom": "Bottom Press", "bottom_release": "Bottom Release", "release_end": "Full Release", "key_1": "Key 1", "key_2": "Key 2", "key_3": "Key 3", "key_4": "Key 4", "advanced_keyrs_title": "Dynamic Keystroke (DKS)", "advanced_keyrs_content": "Select a key from your keyboard or the preview above to bind DKS. This mode can bind up to four actions based on the key press stages: start, bottom, release at bottom, and full release. Choose the key you want to set.", "first_and_fourth_dks_action_execution_point": "Actuation point of press start and full release", "second_and_third_dks_action_execution_point": "Actuation point of bottom press and bottom release"}, "tgl": {"tgl_config": "Toggle Key", "advanced_keyrs_title": "Toggle Key (TGL)", "advanced_keyrs_content": "Tap the key to toggle continuous activation on or off. Holding the key behaves as normal. Drag and drop the key to your desired position to bind the toggle switch."}}, "career_preset": {"description": "Rebuilding in progress. Stay tuned!", "apply_settings": "One-<PERSON><PERSON> Setup", "apply_settings_success": "Done", "career_info": {"tenz_desc": "A Canadian VALORANT professional player. Former CSGO professional, renowned for his precise aim and fluid movement. Played a pivotal role in helping a North American team secure victory at VCT Masters Reykjavik.", "prx_desc": "A Singaporean VALORANT professional player. He led his APAC team win the VCT Pacific championship and finish runner-up at VCT Champions.", "lev_desc": "A Brazilian VALORANT professional player. Widely regarded as the world's best for his excertional skills. Previously player for a top South American team and won the Champions tournament a few years ago.", "nrg_desc": "A North American VALORANT professional player. Valued for his versatile roleplay and sharp utility usage. Contributed to his team’s win at the VCT Masters a few years ago.", "tarik_desc": "An American VALORANT streamer and former CS world champion. Now a popular content creator and tournament host, admired for his game sense and entertaining commentary.", "fnc_desc": "A Russian VALORANT professional player. Made history by winning the Masters."}}, "upgrade_version": {"firmware_update": "Firmware Update", "upgrade_version_tip": "Update to the latest firmware for proper access", "step_1": "Step 1:", "step_1_desc": "Download the latest firmware", "download": "Download", "firmware": "Firmware", "step_2": "Step 2:", "step_2_desc": "Enter DFU Mode", "click_to_enter_dfu": "Click to enter", "step_3": "Step 3:", "open": "Open", "my_computer": "This Computer", "double_click": "double-click", "disk_name": "[IQUNIX_DFU] drive", "copy_or_drag": ", and copy or drag the firmware file there.", "video_guide": "Video Guide", "update_completed": "Update Completed"}, "keytest": {"operation-panel-apm": "Key Presses/min", "reset_pushs": "Reset", "browser_prompt_content": "Due to browser API limitations, some keys may not be testable"}, "settings": {"enter_firmware_upgrade_mode": "Enter Upgrade Mode", "restore_factory_settings": "Reset to De<PERSON>ult", "firmware_version_info": "Firmware Version Info", "current_firmware_version": "Current Version:", "online_upgrade_firmware": "Online Firmware Upgrade", "manual_upgrade_firmware": "Manual Firmware Upgrade", "usage_instructions": "Usage Instructions", "user_guide": "User Guide", "other_settings": "Other Settings", "check_for_updates": "Check For Updates", "new_version_available": "New Version Available", "version_check": "Version Check", "current_version_is_the_latest": "The current version is the latest", "check_for_updates_failed": "Check for updates failed", "please_try_again_later": "Please try again later", "current_version": "Current Version", "latest_version": "Latest Version", "update_log": "Update Log", "download_url": "Download URL", "force_update": "Mandatory Update", "update_modal_content": "Need to update firmware?", "update_modal_cancel": "Not Now", "update_modal_update": "Update Now", "version_update": {"find_new_version": "New Version Found", "update_content": "Update Content：", "please_do_not_disconnect_the_device_during_the_update_process": "Do not disconnect device while updating", "step_1_of_6": "Step 1 of 6", "waiting_for_update_mode_connection": "Waiting for update mode connection", "step_2_of_6": "Step 2 of 6", "allow_browser_to_download_firmware_update": "Allow the browser to download firmware updates", "connect_device": "Connect Device", "step_3_of_6": "Step 3 of 6", "get_firmware_update": "Gets firmware updates.", "step_4_of_6": "Step 4 of 6", "flash_firmware_to_device": "Flash firmware to the device", "step_5_of_6": "Step 5 of 6", "waiting_for_device_connection": "Waiting for device connection", "firmware_update_success": "Firmware update successful", "step_6_of_6": "Step 6 of 6", "firmware_update_failed": "Firmware update failed", "please_check_device_connection_and_try_again": "Please check your device connection and try again", "update_now": "Update Now", "update_later": "Update Later", "update_modal_title": "Firmware Updates"}, "full_key_no_click": "Disable N-Key Rollover (NKRO)", "full_key_no_click_tip": "To ensure compatibility with devices like the PS4, please disable N-Key Rollover (NKRO) and enable 6-Key Rollover (6KRO). When 6KRO is enabled, the keyboard can correctly recognize up to six simultaneous key presses to ensure proper operation on these devices."}, "profile": {"custom_profile": "Custom Profile", "office_profile": "Office Profile", "esport_profile": "eSports Profile", "profile_tooltip": "This mode does not support rapid trigger or key mapping settings"}}