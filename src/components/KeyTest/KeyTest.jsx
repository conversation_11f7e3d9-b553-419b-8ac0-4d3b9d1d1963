import  './Keytest.css'

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import KeyboardTest from './KeyboardTest.jsx';
import KeyTestLogs from './KeyTestLogs.jsx';
import GetKeyVoltage from './GetKeyVoltage';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../Keyboard/KeyboardContext.jsx';
import { Tabs } from 'antd';


const KeyTest = ({deviceProductId}) => {
  const { t } = useTranslation();
  const [ flag, setFlag ] = useState('logs'); //控制按钮
  const { dataQueue, setAdjustStatus, setAdjustTimes, setNeedAdjustKeys } = useHandleDevice();
  const [tmpMeedAdjustKeys, setTmpMeedAdjustKeys] = useState([]);
  const { data, updateKeycap } = useKeyboard()
  // 切换tab
  const changeTab= (value)=> {
    const extra_row = data.keycaps["00"].hasOwnProperty("05-01")
    setFlag(value);
    const keys = Object.keys(data.keycaps["00"]);
    if (value === 'fast') {
      setAdjustStatus('');
      setAdjustTimes(2);
      setNeedAdjustKeys([])
      // 清空已累计次数
      keys.forEach(key => {
        updateKeycap(key.split("-")[0], key.split("-")[1], { alreadyAdjustTimes: 0 }, "00");
      });
    } else if (value === 'professional') {
      setAdjustStatus('');
      setAdjustTimes(5);
      // 清空已累计次数
      if (extra_row) {
        const tmpMeedAdjustKeys = ["02-00", "02-02", "03-01", "03-02", "03-03", "04-00", "05-00"]
        setTmpMeedAdjustKeys(tmpMeedAdjustKeys)
        setNeedAdjustKeys(tmpMeedAdjustKeys)
        tmpMeedAdjustKeys.forEach(key => {
          updateKeycap(key.split("-")[0], key.split("-")[1], { alreadyAdjustTimes: 0 }, "00");
        });
      } else {
        const tmpMeedAdjustKeys = ["01-00", "01-02", "02-01", "02-02", "02-03", "03-00", "04-00"]
        setTmpMeedAdjustKeys(tmpMeedAdjustKeys)
        setNeedAdjustKeys(tmpMeedAdjustKeys)
        tmpMeedAdjustKeys.forEach(key => {
          updateKeycap(key.split("-")[0], key.split("-")[1], { alreadyAdjustTimes: 0 }, "00");
        });
      }
    }
  }

  useEffect(() => {
    let intervalId;
    if (flag === 'fast' || flag === 'professional') {
      // 立即执行一次
      GetKeyVoltage(dataQueue);
      // 设置定时器，每100ms执行一次
      intervalId = setInterval(() => {
        GetKeyVoltage(dataQueue);
      }, 100);
    } else {
      clearInterval(intervalId);
    }

    // 清理函数
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [flag, dataQueue]);

  var items = [];
  if([36880, 36869, 32773, 32784].includes(deviceProductId)) {
    items = [
      {
        key: 'logs',
        label: t('keytest_tab.logs')
      },
    ];
  } else {
    items = [
      {
        key: 'logs',
        label: t('keytest_tab.logs')
      },
      {
        key: 'professional',
        label: t('keytest_tab.professional')
      },
      {
        key: 'fast',
        label: t('keytest_tab.fast')
      },
    ];
  }

  return <>
    <div className="keytest-content" >
        <Tabs defaultActiveKey="logs" items={items}  onChange={changeTab} tabBarGutter={150} />
        { flag === 'logs' &&  <KeyTestLogs key="keytestlogs" /> }
        { flag === 'professional' && <KeyboardTest itemPage = {flag} tmpMeedAdjustKeys={tmpMeedAdjustKeys}/> }
        { flag === 'fast' && <KeyboardTest itemPage = {flag} tmpMeedAdjustKeys={tmpMeedAdjustKeys}/> }
    </div>
  </>
}
export default KeyTest;