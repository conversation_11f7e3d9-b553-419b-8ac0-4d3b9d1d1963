import React from 'react';
import classnames from 'classnames';
import  './Keytest.css'
import forbidden from '../../assets/forbidden.svg'

function KeyTestButton({item,
      isSpecialKey,
      keyClickCount,
      status,
      realtime_voltage,
      max_voltage,
      min_voltage,
      keyboardJsonPosition,
      onClick,
      special_icon_base_number=5}) {

  const SPECIAL_ICON_BASE = '/keytest/numbers/';

  const renderValues = () => (
    {
      content: (
        <>
          <span>{realtime_voltage}</span>
          <span className="little">{max_voltage}</span>
          <span className="little">{min_voltage}</span>
        </>
      ),
      no: 1
    }
  );

  const renderIcons = () => {
    // if (!item.col || !item.row) return { content: null, no: 0 };

    const keyPosition = `${item.row[keyboardJsonPosition]}-${item.col[keyboardJsonPosition]}`;
    var clickCount = keyClickCount[keyPosition] || 0;

    // if (clickCount === 0) return { content: null, no: 0 };
    if (clickCount >= special_icon_base_number) {
      clickCount = special_icon_base_number
    }
    return {
      content: (
        <div
          key={`icon-${clickCount}`}
          className="keytest-keycap-button-start"
          style={{
            backgroundImage: `url(${SPECIAL_ICON_BASE + special_icon_base_number + '0'}${special_icon_base_number - clickCount}.svg)`
          }}
        ></div>
      ),
      no: special_icon_base_number - clickCount
    };
  };

  const renderForbidden = () => {
    return {
      content: (
        <div className="keytest-keycap-button-forbidden">
          <img src={forbidden} alt="forbidden" />
        </div>
      ),
      no: 1
    };
  };

  const getContent = () => {
    if (isSpecialKey) {
      switch (status) {
        case 'start':
          return renderIcons();
        case 'pressover':
          return renderIcons();
        default: // none/over
          return renderValues();
      }
    } else {
      return renderForbidden();
    }
  };

  const finalButtonRender = (content) => {
    return <button
      className={classnames('keytest-keycap-button', {
        'active': isSpecialKey,
        'nondeclass': !item.key_value?.trim(),
        'status-active': ['start', 'pressover'].includes(status),
        'finish-calibration': content.no === 0
      })}
      style={{ width: item.width }}
      onClick={onClick}
      data-testid={`key-${item.key_value}`}
    >
      <div className="keytest-keycap-button-item" style={{display: !item.key_value?.trim() && 'none' }} >
        <div className="keytest-keycap-button-item-text">
          <div className="keytest-keycap-button-item-spans">
            {content.content}
          </div>
        </div>
      </div>
    </button>
  }
  return (
    finalButtonRender(getContent())
  );
}

export default KeyTestButton;