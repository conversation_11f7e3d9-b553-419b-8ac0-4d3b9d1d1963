import { <PERSON><PERSON><PERSON>, Mo<PERSON> } from 'antd';
import { useState, useEffect } from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import Keymap from '../../Keymap';
import { findCodeByKey, findNameByCode, parseHex, changeToHighLowHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
import processString from '../../Keyboard/processString' ;
const MtConfig = ({currentStep}) => {
  const [activeKey, setActiveKey] = useState('current');
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const [open, setOpen] = useState(false);
  const currentKey = data.keycaps[data.currentLayer]?.[`${advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_row}-${advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_col}`];
  const newList = [...advancedKey.mt.list];
  const currentItem = newList[advancedKey.selectedIndex];
  const { t } = useTranslation();

  const handleKeyClick = (key) => {
    setActiveKey(key);
    if (key === 'current') {

    } else if (key === 'hold') {
      setOpen(true)
    } else if (key === 'click') {
      setOpen(true)
    }
  }

  useEffect(() => {
    if (currentItem) {
      currentItem.keycap_row = data.currentSelectedKeycaps[0]?.row || 0;
      currentItem.keycap_col = data.currentSelectedKeycaps[0]?.column || 0;
    }
    setAdvancedKey(prev => ({
      ...prev,
      mt: {
        ...prev.mt,
        list: newList
      }
    }))
  }, [data.currentSelectedKeycaps]);

  const handleKeycapClick = (keycapName) => {
    if (activeKey === 'click') {
      currentItem.short_press_code = findCodeByKey(keycapName)
    } else if (activeKey === 'hold') {
      currentItem.long_press_code = findCodeByKey(keycapName)
    }
    setAdvancedKey(prev => ({
      ...prev,
      mt: {
        ...prev.mt,
        list: newList
      }
    }))
    setOpen(false)
  };

  return (
    <>
    <div className="dks-config">
    { currentStep === 2 && currentItem && <>
      <Modal
        // title={t('advanced_key.mt.choose_keyboard')}
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        width={1450}
        className={'keybordmodal'}
      >
        <Keymap handleKeycapClick={handleKeycapClick} />
      </Modal>
      <div className="rs-config-content">
        <div className='advanced_keyrs_title'><span>{t('advanced_key.mt.advanced_keyrs_title')}</span></div>
        <div className='advanced_keyrs_content'><span>{t('advanced_key.mt.advanced_keyrs_content')}</span></div>
      </div>
      <div className="rs-config-item">
        <div className={`rs-config-key-container ${activeKey === 'current' ? 'active' : ''} `} onClick={() => handleKeyClick('current')}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
                <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${currentKey?.label  ?   processString(currentKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
            {
              currentKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="var(--bs-primary)"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
            <span>{ t('advanced_key.dks.key_1')}</span>
            </div>
          </div>
        </div>
        <div className={`dks-config-items-main-item `} >
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_1')}</span></div>
               <div className={`dks-config-items-main-item-key-container ${activeKey === 'hold' ? 'active' : ''} `} onClick={() => handleKeyClick('hold')}>
                  <svg aria-hidden="true"  width="36"  height="36">
                     <use href= {`#icon-${currentItem.long_press_code  && findNameByCode(currentItem.long_press_code) !== 'default' ?   processString(findNameByCode(currentItem.long_press_code),'picture') : 'evenodd'}`}/>
                  </svg>
               </div>
            </div>
            <div className={`dks-config-items-main-item `} >
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_2')}</span></div>
               <div  className={`dks-config-items-main-item-key-container ${activeKey === 'click' ? 'active' : ''} `}  onClick={() => handleKeyClick('click')}>
                  <svg aria-hidden="true"  width="36"  height="36">
                     <use href= {`#icon-${currentItem.short_press_code && findNameByCode(currentItem.short_press_code) !== 'default' ?   processString(findNameByCode(currentItem.short_press_code),'picture') : 'evenodd'}`}/>
                  </svg>
               </div>
            </div>
      </div>
    </> }

    { currentStep === 3 &&   currentItem  &&  <>
      <div className='mt-config-content'>
         <div className="mt-config-content-modes"></div>
         <div className="mt-config-content-bottom">
           <div className='advanced_keyrs_title'><span>{t('advanced_key.mt.advanced_mtmodes_title')}</span></div>
           <div className='advanced_keyrs_content'><span>{t('advanced_key.mt.advanced_mtmodes_content')}</span></div>
           <div className="mt-config-content-slider">
           <Slider defaultValue={parseHex(currentItem.long_press_time)} min={100} max={500} style={{width: '100%'}} onChange={(value) => {
            currentItem.long_press_time = changeToHighLowHex(value)
            setAdvancedKey(prev => ({
               ...prev,
               mt: {
               ...prev.mt,
               list: newList
            }
            }))
           }} />
            <div>{parseHex(currentItem.long_press_time)}ms</div>
         </div>
         </div>

      </div>
    </>}
    </div>
   </>
  )
}

export default MtConfig;