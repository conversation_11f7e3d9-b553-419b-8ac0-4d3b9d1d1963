import { Ta<PERSON>, But<PERSON>, Flex } from 'antd';
import useKeyboardLogic from './useKeyboardLogic';
import  './Keyboard.css';

// 键盘主组件
function Keyboard({layout, background, width, height, paddingTop, paddingLeft}) {
  const {
    data,
    t,
    setCurrentLayer,
    handleMouseLeave,
    renderKeycap,
    getKeyLabel,
    getKeyAdvancedType,
    handleSelectWASD,
    handleSelectAll,
    handleInvertSelection,
    handleClearSelection
  } = useKeyboardLogic(layout);

  return (
    <div className='keyboardStyle'>
      <div className='containerStyle'>
        <div className='layerSwitchStyle'>
          {data.showLayerSwitch && (
            <Tabs
              tabPosition='left'
              activeKey={data.currentLayer}
              onChange={setCurrentLayer}
              items={[
                {
                  key: "00",
                  label: t('keyboard.main_layer'),
                },
                {
                  key: "01",
                  label: t('keyboard.secondly_layer'),
                }
              ]}
            />
          )}
        </div>
        <div style={{
          backgroundImage: `url(${background})`,
          backgroundSize: "100% 100%",
          backgroundPosition: "center",
          width: width,
          height: height,
          paddingTop: paddingTop,
          paddingLeft: paddingLeft,
          marginBottom: '1em'
        }}
          onMouseLeave={handleMouseLeave}>
          {layout?.map((rowObj, rowIdx) => (
            <div
              key={rowIdx}
              className='rowStyle'
              style={rowObj.style}
            >
              {rowObj.keys.map((key, colIdx) => (
                renderKeycap({
                  row: key.row,
                  column: key.column,
                  size: key.size,
                  label: getKeyLabel(key.row, key.column),
                  style: key.style,
                  advancedKeyType: getKeyAdvancedType(key.row, key.column)
                })
              ))}
            </div>
          ))}
        </div>
        <div className='buttonGroupStyle'>
          {data.showSelectButton && (
            <Flex gap="middle" vertical>
              <Button onClick={handleSelectWASD} style={{ marginBottom: '1em', borderColor: 'transparent', color: '#A3A3A3', fontFamily: 'Anton' }}>WASD</Button>
              <Button onClick={handleSelectAll} type='default' style={{ backgroundColor: '#151619', borderColor: 'transparent', color: '#A3A3A3' }} autoInsertSpace={false}>{t('keyboard.select_all')}</Button>
              <Button onClick={handleInvertSelection} type='default' style={{ backgroundColor: '#151619', borderColor: 'transparent', color: '#A3A3A3' }} autoInsertSpace={false}>{t('keyboard.invert_selection')}</Button>
              <Button onClick={handleClearSelection} type='default' style={{ backgroundColor: '#151619', borderColor: 'transparent', color: '#A3A3A3' }} autoInsertSpace={false}>{t('keyboard.clear_selection')}</Button>
            </Flex>
          )}
        </div>
      </div>
    </div>
  );
}

export default Keyboard;