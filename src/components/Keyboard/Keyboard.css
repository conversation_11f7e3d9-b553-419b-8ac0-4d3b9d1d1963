.keyboardStyle{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.keyboardStyle *{
box-sizing: border-box;
}
.containerStyle{
    display: flex;
    padding: 10px;
    margin-top: 40px;
    align-items: center;
}
.buttonGroupStyle {
    margin-bottom: 1em;
    margin-left: 2em;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
    width: 60px;
}
.rowStyle {
    display: flex;
}
.layerSwitchStyle{
    margin-bottom: 1em;
    height: 46px;
    width: 120px;
}

span.keycap-inside-dou{
    /* height: 28px; */
    display: flex;
    min-height: 28px;
    width: 100%;
    /* font-family: vivo Sans; */
    font-weight: 200;
    font-size: 10px;
    line-height: 12px;
    letter-spacing: 0%;
    text-align: center;
    align-items: center;
    background: #0B0C0E !important;
    color: var(--32, #EFF0F559);
    border-radius: 2px 2px 0 0;
    justify-content: center;
    /* border-left: 2px solid #0B0C0E;
    border-right: 2px solid #0B0C0E;
    border-bottom: 2px solid #0B0C0E; */
}
.keycap-dou{
    flex-direction: column;
    /* height: 56px !important; */
    border: 2px solid transparent;
    border-image: linear-gradient(to bottom, #17181C, #17181C 50%, #0B0C0E 50%, #0B0C0E) 1;
    /* border-image: linear-gradient(to bottom, #ff0000, #ff0000 50%, #0000ff 50%, #0000ff) 1; */
    /* background-clip: padding-box;
    background-image: linear-gradient(to right, red, blue);  */
    /* background: none; */
}
.keycap-dou.active {
    flex-direction: column;
    border: 2px solid #ff5656;
}

span.keycap-inside-up{
    display: flex;
    width: 100%;
    height: 100%;
    text-align: center;
    /* font-family: vivo Sans; */
    font-weight: 400;
    font-size: 13px;
    line-height: 24px;
    letter-spacing: 0%;
    overflow: hidden;
    justify-content: center;
    background: linear-gradient(0deg, var(--3, #17181C), var(--3, #17181C)),
linear-gradient(0deg, var(--4, rgba(239, 240, 245, 0.04)), var(--4, rgba(239, 240, 245, 0.04)));
    color: var(--1, #EFF0F5D9);

}
.keycap-u1 .keycap-inside{
    flex-direction: column;
}
.keycap-dou-in{
    display: flex;
    flex-direction: column;
    /* height: 56px ; */
    width: 100%;
    justify-content: center;
    align-items: center;
    justify-items: center;

}

.keyicon_class{
    line-height: 36px;
    width: 42px;
    height: 42px;
    /* background-image: url(/src/assets/keyboards/icons/esc-icon.svg); */
    background-size: cover;
    background-repeat: no-repeat;
    text-align: center;
    justify-items: center;
    z-index: 1;
    font-family: MiSans;
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0%;
    vertical-align: middle;
    /* color: #8C8D91; */
    color: transparent;
}