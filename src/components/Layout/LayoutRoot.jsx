import { Layout, notification, Dropdown, ConfigProvider } from 'antd';
import Sidebar from './Sidebar';
import Keyboard from '../Keyboard';
import KeymapConfig from '../Keymap/KeymapConfig';
import Light from '../Light';
import Performance from '../Performance';
import AdvancedKey from '../AdvancedKey';
import CareerPreset from '../CareerPreset';
import KeyTest from '../KeyTest';
import Settings from '../Settings';
import Logo from '../../assets/logo.svg'
const { Header, Content, Sider } = Layout;
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useState, useContext, useEffect } from 'react';
import { Spin, Modal, Button, Divider } from 'antd';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { DownloadOutlined, ToolOutlined, FullscreenOutlined } from '@ant-design/icons';
import my_computer from '../../assets/my_computer.png';
import cipan from '../../assets/cipan.png';
import { useTranslation } from 'react-i18next';
import Profile from '../Profile';
import keyboard_bg from '../../assets/keyboard_bg.png';
import VersionUpdate from '../Settings/VersionUpdate';
import { HandleLayout } from '../Keyboard/HandleLayout';
import EzHomePage from '../Home/EzHomePage';
import EvHomePage from '../Home/EvHomePage';

const UpdateFirmwareModal = ({ open, firmwareFile, firmwareName, onOk, onEnterDFU, onCancel }) => {
  const { t, i18n } = useTranslation();

  return (
    <Modal open={open} closable={true} onCancel={onCancel} footer={[
      <Button key="updateCancel" onClick={onCancel}>
        {t('settings.update_modal_cancel')}
      </Button>,
      <Button key="updateOk" onClick={onOk}>
        {t('upgrade_version.update_completed')}
      </Button>
    ]} styles={{ mask: { backdropFilter: 'blur(10px)' }}}>
      <h1>{t('upgrade_version.firmware_update')}</h1>
      {/* <p>{t('upgrade_version.upgrade_version_tip')}</p> */}
      <div style={{ backgroundColor: '#ffffff', height: '1px', border: 'none', margin: '10px 0', opacity: '0.2' }}></div>
      <div style={{ margin: '50px 0' }}>
        <h2>{t('upgrade_version.step_1')}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{t('upgrade_version.step_1_desc')}</h2>
        <Button type="primary" icon={<DownloadOutlined />} href={firmwareFile}>{t('upgrade_version.download')} {firmwareName} {t('upgrade_version.firmware')}</Button>
      </div>
      <div style={{ margin: '50px 0' }}>
        <h2>{t('upgrade_version.step_2')}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{t('upgrade_version.step_2_desc')}</h2>
        <Button type="primary" icon={<ToolOutlined />} onClick={onEnterDFU}>{t('upgrade_version.click_to_enter_dfu')}</Button>
      </div>
      <div style={{ margin: '50px 0' }}>
        <h2>{t('upgrade_version.step_3')}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{t('upgrade_version.open')} [ <img src={my_computer} alt="my_computer" style={{ width: '26px', height: 'auto', verticalAlign: 'sub', marginRight: '5px' }} />{t('upgrade_version.my_computer')} ]，{t('upgrade_version.double_click')} [ <img src={cipan} alt="cipan" style={{ width: '34px', height: 'auto', verticalAlign: 'middle', marginRight: '5px' }} />{t('upgrade_version.disk_name')} ]{t('upgrade_version.copy_or_drag')}</h2>
      </div>
      <div style={{display: 'flex', justifyContent: 'center', marginBottom: '5px', fontSize: '1.2em', color: '#ffffff'}}>{t('upgrade_version.video_guide')}</div>
      <div>
      {(i18n.language === "zh-CN") ?
        <video controls src="https://ez.iqunix.com/tutorial.mp4" style={{width: '100%', height: '100%'}}></video>
        :
        <iframe
          width="100%"
          height="260"
          src="https://www.youtube.com/embed/UOLuFbhAWIQ?si=i7DClnoOnPXIDaZw"
          title="YouTube video player"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerpolicy="strict-origin-when-cross-origin"
          allowfullscreen
        ></iframe>
      }
      </div>
    </Modal>
  );
};

const LayoutRoot = () => {
  const { colorBgContainer, borderRadiusLG } = { colorBgContainer: "#000000", borderRadiusLG: "16px" };
  const { data } = useContext(KeyboardContext);
  const { deviceProductId, keyboardLoading, fullScreenLoading, fullScreenPercent, setKeyboardLoading, device,
    handleOpenDevice, fullScreenLoadingText, contentLoading, firmwareVersion, updatingFirmware,
    setUpdatingFirmware, newVersion, setNewVersion, updateModalVisible, setUpdateModalVisible, latestVersion,
    setLatestVersion, setDownloadUrl, forceUpdate, setForceUpdate, setUpdateLog, showEnterDriver, setShowEnterDriver } = useHandleDevice();
  const [updateEz60Modal, setUpdateEz60Modal] = useState(false);
  const [updateEz63Modal, setUpdateEz63Modal] = useState(false);
  const [api, contextHolder] = notification.useNotification();
  const [showHome, setShowHome] = useState(true);
  const [homeLoading, setHomeLoading] = useState(false);
  const { t, i18n } = useTranslation();
  // const [notificationEz6063, setNotificationEz6063] = useState(false);
  useEffect(() => {
    if (import.meta.env.VITE_PASSWORD_VERIFIED === 'true') {
      const verifiedTime = localStorage.getItem('key_t');
      const currentTime = new Date().getTime();

      if (!verifiedTime || currentTime - parseInt(verifiedTime) > 24 * 60 * 60 * 1000) {
        let testV = 1;
        let pass1 = prompt('', '');
        const validPasswords = [
          "ZXo2NjY2NjZleg"
        ];

        while (testV < 3) {
          if (!pass1) {
            history.go(-1);
            return;
          }

          if (validPasswords.some(pwd => atob(pwd) === pass1)) {
            localStorage.setItem('key_t', currentTime.toString());
            break;
          }

          testV += -1;
          pass1 = prompt('fail:');
        }

        if (!validPasswords.some(pwd => atob(pwd) === pass1) && testV === 3) {
          history.go(-1);
          return;
        }
      }
    }
  }, []);

  useEffect(() => {
    setKeyboardLoading(true);
    setTimeout(() => {
      setKeyboardLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    if (import.meta.env.VITE_REDIRECT_TO_EZ === 'true') {
      window.location.href = import.meta.env.VITE_REDIRECT_URL;
    } else {
      if (deviceProductId === 32784) {
        setUpdateEz60Modal(true);
      } else if (deviceProductId === 32773) {
        setUpdateEz63Modal(true);
      }
      if (!updatingFirmware) {
        if (deviceProductId === null) {
          setShowHome(true);
          setUpdateModalVisible(false);
        } else {
          setHomeLoading(true);
          setTimeout(() => {
            api.warning({
              message: t('home.find_device'),
              description:
                `${t('home.find_device_tip')} ${device.productName}`,
              duration: 2.5,
              pauseOnHover: false,
            });
            setTimeout(() => {
              api.success({
                message: t('home.device_initialized'),
                description:
                  `${device.productName} ${t('home.device_loaded')}`,
                duration: 2.5,
                pauseOnHover: false,
              });
              // setShowHome(false);
              setShowEnterDriver(true);
              setHomeLoading(false);
            }, 2500);
          }, 1000);
        }
      }
    }
  }, [deviceProductId]);

  useEffect(() => {
    // if ([36880, 36869, 32773, 32784].includes(deviceProductId)) {
    //   setNotificationEz6063(true);
    // }
  }, [deviceProductId]);

  useEffect(() => {
    // 设定期望的固件版本号
    const expectedEz60Version = "1.0.9";
    const expectedEz63Version = "1.1.5";

    if (deviceProductId === 36880 && firmwareVersion !== null && compareFirmwareVersions(firmwareVersion, expectedEz60Version) < 0) {
      setUpdateEz60Modal(true);
    } else if (deviceProductId === 36869 && firmwareVersion !== null && compareFirmwareVersions(firmwareVersion, expectedEz63Version) < 0) {
      setUpdateEz63Modal(true);
    } else {
      setUpdateEz60Modal(false);
      setUpdateEz63Modal(false);
    }

  }, [firmwareVersion]);

  useEffect(() => {
    if ([9010, 25344, 25569, 29952, 26624].includes(deviceProductId) && firmwareVersion !== null) {
      const checkVersion = async () => {
        const checkVersion = await fetch(`${import.meta.env.VITE_API_URL}/api/firmware_check/firmware_check?device_id=${deviceProductId}&deploy_env=${import.meta.env.VITE_API_ENV}`);
        const checkVersionData = await checkVersion.json();
        const compareVersions = (v1, v2) => {
          const v1Parts = v1.split('.').map(Number);
          const v2Parts = v2.split('.').map(Number);

          for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;
            if (v1Part > v2Part) return 1;
            if (v1Part < v2Part) return -1;
          }
          return 0;
        };

        setLatestVersion(checkVersionData.version);

        setDownloadUrl(checkVersionData.download_url);
        setUpdateLog(checkVersionData.update_log);
        if (compareVersions(checkVersionData.version, firmwareVersion) > 0) {
          setNewVersion(checkVersionData.version);
        }
        if ((compareVersions(checkVersionData.version, firmwareVersion) > 0) && checkVersionData.force_update) {
          setUpdateModalVisible(true);
          setForceUpdate(true);
        }
      }
      checkVersion();
    }
  }, [firmwareVersion]);

  const reloadWindows = () => {
    window.location.reload();
  }

  // 旧键盘进入DFU模式
  const enterDFU = async () => {
    try {
      const outputData = new Uint8Array(32);
      outputData[0] = 0x0B; // First byte is 0B, rest are 00
      await device.sendReport(0, outputData);
      console.log("DFU command sent successfully");
    } catch (error) {
      console.error("Failed to send DFU command:", error);
    }
  }

  const compareFirmwareVersions = (v1, v2) => {
    const v1Parts = v1.split('.').map(Number);
    const v2Parts = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    return 0;
  };

  return (
    <ConfigProvider
          theme={{
            token: {
              fontFamily: 'vivoSans-Regular' ,
            },
          }}
        >
      <Layout>
      {contextHolder}
      <UpdateFirmwareModal
        open={updateEz60Modal}
        firmwareFile={`${import.meta.env.VITE_WEBSITE_URL}/EZ60_0703_V1.0.9.uf2`}
        firmwareName="EZ60"
        onOk={reloadWindows}
        onEnterDFU={enterDFU}
        onCancel={() => setUpdateEz60Modal(false)}
      />
      <UpdateFirmwareModal
        open={updateEz63Modal}
        firmwareFile={`${import.meta.env.VITE_WEBSITE_URL}/EZ63_0703_V1.1.5.uf2`}
        firmwareName="EZ63"
        onOk={reloadWindows}
        onEnterDFU={enterDFU}
        onCancel={() => setUpdateEz63Modal(false)}
      />

      <VersionUpdate latestVersion={latestVersion} setLatestVersion={setLatestVersion} />
      {/* <Modal
        open={notificationEz6063}
        onCancel={() => setNotificationEz6063(false)}
        closable={false}
        footer={[
          <Button key="cancel" onClick={() => setNotificationEz6063(false)}>
            确认
          </Button>
        ]}
      >
        <div>
          <p>EZ60/EZ63 固件已推送到公测版驱动，请到到下方测试驱动页面进行更新体验</p>
          <a href='https://beta.iqunix.com'>HTTPS://BETA.IQUNIX.COM</a>
        </div>
      </Modal> */}
      <div>
        <ConfigProvider
          theme={{
            token: {
              colorBgMask: 'rgba(0, 0, 0, 0.65)',
            },
          }}
        >
          <Spin
            spinning={fullScreenLoading}
            delay={200}
            percent={fullScreenPercent}
            tip={fullScreenLoadingText}
            fullscreen
          />
        </ConfigProvider>
        <Spin spinning={homeLoading} tip={t('home.initializing')} fullscreen />
        <Header
          style={{
            padding: '2.5em 4em',
            background: "#000000",
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '0.5px solid #5e5e5e',
          }}
        >
          <div style={{ marginTop: "0em", display: "flex", justifyContent: "center", alignItems: "center" }}>
            <img src={Logo} alt="logo" style={{ width: "100px", height: "auto", cursor: "pointer" }} />
            <div style={{fontSize: '1em', color: '#ff4d4f', marginLeft: '10px', marginTop: '-16px'}}>{import.meta.env.VITE_APP_ENV}</div>
          </div>
          <div style={{display: showHome ? 'none' : 'flex'}}>
            <Profile />
          </div>
          <div style={{display: 'flex', alignItems: 'center', gap: '20px'}}>
            <Button
              icon={<FullscreenOutlined />}
              onClick={() => {
                if (!document.fullscreenElement) {
                  document.documentElement.requestFullscreen();
                } else {
                  document.exitFullscreen();
                }
              }}
            />
            <Dropdown
              menu={{
                items: [
                  {
                    label: '简体中文',
                    key: 'zh-CN',
                  },
                  {
                    label: '繁体中文',
                    key: 'zh-TW',
                  },
                  {
                    label: 'English',
                    key: 'en-US',
                  },
                  {
                    label: '日本語',
                    key: 'ja-JP',
                  },
                  {
                    label: '한국어',
                    key: 'ko-KR',
                  }
                ],
                onClick: (e) => {
                  i18n.changeLanguage(e.key)
                }
              }}
            >
              <a onClick={(e) => e.preventDefault()}>
                {t('language')}
              </a>
            </Dropdown>
          </div>
        </Header>
        <Layout style={{display: showHome ? 'block' : 'none', position: 'relative'}}>
          <EzHomePage showHome={showHome} setShowHome={setShowHome} setShowEnterDriver={setShowEnterDriver} showEnterDriver={showEnterDriver} />
          {/* <EvHomePage /> */}
        </Layout>
        <Layout style={{display: showHome ? 'none' : 'flex'}}>
          <Sider
            breakpoint="lg"
            collapsedWidth="0"
            onBreakpoint={(broken) => {
            }}
            style={{ height: "calc(100vh - 74px)", backgroundColor: "#000000" }}
            onCollapse={(collapsed, type) => {
            }}
          >
            <Sidebar />
          </Sider>
          <Content
            style={{
              padding: '0px 16px',
              background: "#000000",
            }}
          >
            <div
              style={{
                padding: 24,
                minHeight: "calc(100vh - 71px)",
                background: "#0B0C0E",
                borderRadius: borderRadiusLG,
              }}
            >

              <div
                style={{
                  cursor: 'auto',
                  pointerEvents: 'auto',
                  opacity: 1,
                  position: 'relative'
                }}
              >
                {/* {contentLoading && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      zIndex: 1000,
                    }}
                  />
                )} */}
                {data.showKeyboard && (
                  <Spin spinning={keyboardLoading} style={{height: '1000px', width: '100%'}}>
                    <div style={{background: `url(${keyboard_bg})`, backgroundSize: 'cover', backgroundPosition: 'center', height: '100%', width: '100%'}}>
                      <HandleLayout pid={deviceProductId} />
                    </div>
                  </Spin>
                )}
                {data.menuItem === 'keymap' ? <KeymapConfig /> : null}
                {data.menuItem === 'light' ? <Light /> : null}
                {data.menuItem === 'performance' ? <Performance /> : null}
                {data.menuItem === 'advancedKey' ? <AdvancedKey /> : null}
                {data.menuItem === 'careerPreset' ? <CareerPreset /> : null}
                {data.menuItem === 'keytest' ? <KeyTest deviceProductId={deviceProductId} /> : null}
              </div>
              {data.menuItem === 'settings' ? <Settings /> : null}
            </div>
          </Content>
        </Layout>
      </div>
     </Layout>
    </ConfigProvider>
  );
};
export default LayoutRoot;
