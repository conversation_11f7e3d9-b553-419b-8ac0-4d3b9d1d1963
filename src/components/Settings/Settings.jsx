import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Steps, Progress, Spin, Result, Switch } from 'antd';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

const Settings = () => {
  const { t } = useTranslation();
  const { firmwareVersion, addToQueue, newVersion, deviceProductId, setUpdateModalVisible, setUpdateLog, sn, fullKeyNoClick, setFullKeyNoClick } = useHandleDevice();
  const supportOnlineUpdate = [9010, 4660, 25344, 25569, 25570, 25345, 29952, 29953, 26624]

  const handleFullKeyNoClickChange = (checked) => {
    setFullKeyNoClick(checked);
    addToQueue(`07 00 0B ${checked ? '00' : '01'}`)
  }

  const checkForUpdates = async () => {
    try {
      // 请求后台获取最新版本号
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/firmware_check/firmware_check?device_id=${deviceProductId}&deploy_env=${import.meta.env.VITE_API_ENV}`);
      const data = await response.json();
      const latestVer = data.version;
      // 比较版本号
      const compareVersions = (v1, v2) => {
        const v1Parts = v1.split('.').map(Number);
        const v2Parts = v2.split('.').map(Number);

        for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
          const v1Part = v1Parts[i] || 0;
          const v2Part = v2Parts[i] || 0;
          if (v1Part > v2Part) return 1;
          if (v1Part < v2Part) return -1;
        }
        return 0;
      };

      if (compareVersions(latestVer, firmwareVersion) > 0) {
        setUpdateModalVisible(true);
        setUpdateLog(data.update_log);
      } else {
        Modal.info({
          title: t('settings.version_check'),
          content: t('settings.current_version_is_the_latest')
        });
      }
    } catch (error) {
      Modal.error({
        title: t('settings.check_for_updates_failed'),
        content: t('settings.please_try_again_later')
      });
    }
  };

  const handleEnterFirmwareUpdateMode = () => {
    addToQueue('07 00 02 01')
  }

  return (
    <div style={{ padding: "1em", marginTop: "2em", display: "flex", justifyContent: "center", flexDirection: 'column', alignItems: 'center' }}>
      <div style={{width: '1094px', marginBottom: '2em'}}>
        {newVersion && (
          <Alert
            message={t('settings.new_version_available')}
            type="info"
            closable
          />
        )}
      </div>

      <div style={{ width: "1064px", fontSize: "18px" }}>
        <div style={{ fontWeight: "bold" }}>{t('settings.firmware_version_info')}</div>
        <div style={{ fontSize: "16px", color: "#EFF0F5", opacity: "0.65", marginTop: "0.5em" }}>{t('settings.current_firmware_version')} IQUNIX FIRMWARE {firmwareVersion}</div>
        <div style={{ fontSize: "16px", color: "#EFF0F5", opacity: "0.65", marginTop: "0.5em" }}>S/N: {sn}</div>
        <div style={{ borderRadius: "10px" }}>
          {(supportOnlineUpdate.includes(deviceProductId) || deviceProductId === null) && (
            <div style={{ marginTop: "2.6em", display: "flex", justifyContent: "space-between" }}>
            <div style={{ fontSize: "16px", color: "#EFF0F5", opacity: "0.65", fontWeight: "bold", marginTop: "0.3em" }}><span style={{ marginRight: "0.8em" }}>{'>'}</span> {t('settings.online_upgrade_firmware')}</div>
            <div>
              <Button type="primary" size="large" style={{ width: "178px", height: "36px" }} onClick={checkForUpdates}>{t('settings.check_for_updates')}</Button>
              </div>
            </div>
          )}
          {(!supportOnlineUpdate.includes(deviceProductId) && deviceProductId !== null) && (
            <div style={{ marginTop: "1.5em", marginBottom: "2.5em", display: "flex", justifyContent: "space-between" }}>
              <div style={{ fontSize: "16px", color: "#EFF0F5", opacity: "0.65", fontWeight: "bold", marginTop: "0.3em" }}><span style={{ marginRight: "0.8em" }}>{'>'}</span> {t('settings.manual_upgrade_firmware')}</div>
              <div>
              <Button size="large" style={{ width: "178px", height: "36px" }} onClick={handleEnterFirmwareUpdateMode}>{t('settings.enter_firmware_upgrade_mode')}</Button>
              </div>
            </div>
          )}
        </div>
        <Divider />
        <div style={{ marginTop: "2em", display: "flex", justifyContent: "space-between" }}>
          <div>{t('settings.full_key_no_click')}</div>
          <div>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
              checked={fullKeyNoClick}
              onChange={handleFullKeyNoClickChange}
            />
            </div>
        </div>
        <div style={{
          fontSize: "16px",
          marginTop: "10px",
          color: "#EFF0F5",
          opacity: "0.65",
        }}>
          {t('settings.full_key_no_click_tip')}
        </div>
        <div style={{ marginTop: "1.5em", display: "flex", justifyContent: "space-between" }}>
          <div>{t('settings.other_settings')}</div>
          <div>
            <Button size="large" style={{ width: "178px", height: "36px" }} onClick={() => addToQueue('07 00 01 01')}>{t('settings.restore_factory_settings')}</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;