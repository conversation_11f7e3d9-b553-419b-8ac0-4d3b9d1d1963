const SetSn = (hexArray, setSn) => {
  const sn = parseSn(hexArray[2]) + parseSn(hexArray[3]) + parseSn(hexArray[4]) + parseSn(hexArray[5]) +
             parseSn(hexArray[6]) + parseSn(hexArray[7]) + parseSn(hexArray[8]) + parseSn(hexArray[9]) +
             parseSn(hexArray[10]) + parseSn(hexArray[11]) + parseSn(hexArray[12]) + parseSn(hexArray[13]) +
             parseSn(hexArray[14]) + parseSn(hexArray[15]) + parseSn(hexArray[16]) + parseSn(hexArray[17]);
  setSn(sn);
};

const parseSn = (sn) => {
  const snInt = parseInt(sn, 16);
  return snInt.toString();
};

export default SetSn;