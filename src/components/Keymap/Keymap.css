.center-container{
    width: 1348px;
    height: 555px;
    min-width: 1200px;
    max-width: 1348px;
    gap: 10px;
    padding-right: 24px;
    padding-left: 24px;
    display: flex;
    flex-wrap: wrap;
}
.center-container * {
    box-sizing: border-box;
}
.header{
    display: flex;
    align-items: center;
    min-width: 1125px;
    width: 1300px;
    height: 64px;
    gap: 10px;
    padding: 16px;
}
.header-left {
    display: flex;
    width: 237.5px;
    flex-shrink: 1;
    gap: 8px;
}
.header-right{
    flex: 1;
    display: flex;
    gap: 10px;
}
.flex-item{
    flex: 1;
    text-align: center;
}
.title-cover1{
    width: 24px;
    height: 4px;
    gap: 10px;
    background-color: var(--bs-primary); /* 竖线的颜色 */
    transform: rotate(90deg); /* 将元素旋转90度，使其成为竖线 */
    transform-origin: left top;
}
.title-cover{
    position: relative;
    width: 24px;
    height: 24px;
    gap: 10px;
}
.title-cover::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background-color: var(--bs-primary);
    transform: translateX(-50%);
  }
.button-item{
    /* font-family: vivo Sans; */
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0%;
    color: var(--1, #EFF0F5D9);
}
button.button_change{
    width: 146px;
    height: 36px;
    gap: 8px;
}
.title span{
    /* font-family: vivo Sans; */
    font-weight: 550;
    font-size: 22px;
    line-height: 24px;
    letter-spacing: 0%;
}
.keyboard-content{
    width: 1300px;
    height: 480px;
    gap: 10px;
    padding-top: 24px;
    padding-bottom: 24px;
    border-radius: 16px;
    background-color: #17181C;
    padding: 32px 100px;
    display: flex;
    align-content: center;
    /* overflow: auto; */
}
.keyboard-mouse{
    width: 1076px;
    height: 420px;
    max-height: 420px;
    padding-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-content: flex-start;
}
.keyboard-board{
    width: 1148.5px;
    height: 420px;
    max-height: 420px;
    /* padding-top: 16px; */
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    /* gap: 4px; */
}
.keycap-umarge{
     margin: 0;
     border: 1px solid var(--2, #282A33);
     height: 48px;
     width: 48px;
     flex-direction: column;
}
.keycap-inside-bg{
    word-wrap: break-word;
    word-break: break-word;
    overflow: hidden;
    font-size: 0.9em;
    background: #0B0C0E;
    transition: all 300ms ease-in-out !important;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;
    width: 100%;
    /* gap: 10px; */
    border-radius: 4px;
    border-width: 1px;
   flex-direction: column;
}
span.keycap-inside-bg{
    /* font-family: vivo Sans; */
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);
}
.keycap-inside-bg span{
    /* font-family: vivo Sans; */
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);
}
.last-keycap{
    display: flex;
    gap: 4px;
}
.flex-item button{
    width: 100%;
}
button.active{
    color: var(--bs-primary);
    border-color: var(--bs-primary);
    background: #141414;
}
.mouse-click{
    width: 166px;
    height: 132px;
    gap: 24px;
    border-radius: 4px;
    padding-top: 24px;
    padding-bottom: 16px;
    border-width: 1px;
    background: #0B0C0E;
    border: 1px solid var(--2, #282A33);
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
    min-width: 166px;
    max-width: 166px;
}
.mouse-cover {
   width: 100%;
   display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
}
.mouse-cover img{
    width: 48px;
    height: 48px;
}
.mouse-text span{
    /* font-family: vivo Sans; */
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);

 }
.keyboard-multimedia{
    width: 1076px;
    height: 240px;
    max-height: 420px;
    padding-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-content: flex-start;
}
.multimedia-click{
    width: 166px;
    height: 96px;
    gap: 10px;
    border-radius: 4px;
    padding-top: 24px;
    padding-bottom: 16px;
    border-width: 1px;
    background: #0B0C0E;
    border: 1px solid var(--2, #282A33);
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
    min-width: 166px;
    max-width: 166px;
}
.light-click{
    width: 166px;
    height: 96px;
    gap: 10px;
    border-radius: 4px;
    padding-top: 24px;
    padding-bottom: 16px;
    border-width: 1px;
    background: #0B0C0E;
    border: 1px solid var(--2, #282A33);
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
    min-width: 166px;
    max-width: 166px;
}
.multimedia-cover {
    width: 100%;
    display: flex;
     width: 100%;
     align-items: center;
     justify-content: center;
 }
 .multimedia-cover img{
     width: 24px;
     height: 24px;
 }
 .multimedia-text span{
    /* font-family: vivo Sans; */
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);

 }
 .keyboard-layer{
    width: 1076px;
    height: 322px;
    gap: 18px;
    padding-top: 32px;
    display: flex;
    flex-wrap: wrap;
 }
 .layer-text-title{
    width: 100%;
    /* width: 1076; */
    height: 22px;
 }
 .layer-click{
    width: 166px;
    height: 96px;
    gap: 10px;
    border-radius: 4px;
    padding-top: 24px;
    padding-bottom: 16px;
    border-width: 1px;
    background: #0B0C0E;
    border: 1px solid var(--2, #282A33);
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
    min-width: 166px;
    max-width: 166px;
}
.layer-cover {
    width: 100%;
    display: flex;
     width: 100%;
     align-items: center;
     justify-content: center;
 }
 .layer-cover img{
     width: 24px;
     height: 24px;
 }
 .layer-text span{
    /* font-family: vivo Sans; */
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);

 }
 .keyboard-other{
    width: 1076px;
    height: 749px;
    gap: 10px;
    padding-top: 32px;
    display: flex;
    flex-wrap: wrap;
  }
  .other-text-title{
    width: 100%;
    /* width: 1076; */
    height: 22px;
  }
  .other-click{
    width: 166px;
    height: 96px;
    gap: 10px;
    border-radius: 4px;
    padding-top: 24px;
    padding-bottom: 16px;
    border-width: 1px;
    background: #0B0C0E;
    border: 1px solid var(--2, #282A33);
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
    min-width: 166px;
    max-width: 166px;
  }
  .other-cover {
    width: 100%;
    display: flex;
     width: 100%;
     align-items: center;
     justify-content: center;
  }
  .other-cover img{
     width: 24px;
     height: 24px;
  }
  img.other-click-cover {
    width: 32px;
  }
  .other-text span{
    /* font-family: vivo Sans; */
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--3, #EFF0F573);

  }
  .keyboard-other-content{
    display: flex;
    height: 416px;
    overflow: auto;
  }
  div.other-click:hover,div.mouse-click:hover,div.multimedia-click:hover,div.light-click:hover,div.layer-click:hover{
    border: 1px solid #353535;
    cursor: pointer;
    transition: all 300ms ease-in-out !important;
    background: linear-gradient(180deg, rgba(0,0,0,1) 50%, rgba(29,29,29,1) 100%) !important;
  }
  .keycap-inside-bg:hover {
    transition: all 300ms ease-in-out !important;
    background: linear-gradient(180deg, rgba(0,0,0,1) 50%, rgba(29,29,29,1) 100%) !important;
  }
  .keycap-umarge:hover {
    border: 1px solid #353535;
  }
  .keyboard-other-content::-webkit-scrollbar {
    width: 5px;
  }
  .keyboard-other-content::-webkit-scrollbar-thumb {
    background: #4e4e4e;
    border-radius: 25px;
  }
  .keyboard-other-content::-webkit-scrollbar-track {
    background-color: black;
}
.keybordmodal  .ant-modal-content {
    background: #17181C !important;
    border: 1px solid #393a40  !important;
}
.keycap-inside-bg.active,.mouse-click.active,.multimedia-click.active,.light-click.active,.other-click.active,.layer-click.active{
    /* border: 1px solid var(--bs-primary) !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 1) 50%, rgb(99, 140, 189) 100%) !important; */
    border: 1px solid var(--bs-primary) !important;
    /* cursor: pointer; */
    transition: all 300ms ease-in-out !important;
    background: linear-gradient(180deg, rgba(0,0,0,1) 50%, rgba(29,29,29,1) 100%) !important;
}
.specil-keycap{
    display: flex;
    gap: 4px;
    width: 932px;
    height: 100px;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 12px;
    /* justify-content: space-between; */
}
.nondeclass{
    background: none;
    border: none;
}
.nondeclass .keycap-inside-bg{
    display: none;
}
.main-keycap{
    width: 776px;
    height: 308px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1px;
}
.middle-keycap{
    width: 152px;
    height: 308px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    /* align-content: flex-start; */
    align-content: space-around;
}
.number-keycap{
    width: 204px;
    height: 256px;
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    flex-direction: row;
}
.basic-keycap{
    width: 1148.5px;
    height: 308px;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap:10px
}
.three-number-keycap{
    display: flex;
    flex-direction: row;
    flex: 1;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-around;
}
.last-col-keycap{
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    /* justify-content: space-between;
    align-content: space-around; */
    justify-content: space-around;
}
.nondeclass:hover{
    border: none;
    background: none;
    cursor: default;

}